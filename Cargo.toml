[package]
name = "pixiv-tag-downloader"
version = "0.1.0"
edition = "2021"
authors = ["Mannix Sun <<EMAIL>>"]
description = "Pixiv 标签下载器 - 根据标签下载 Pixiv 用户作品的 Rust 应用程序"
license = "MIT"
repository = "https://github.com/mannixsun/PixivTagDownloader_Rust"
keywords = ["pixiv", "downloader", "artwork", "rust", "async"]
categories = ["command-line-utilities", "multimedia"]

[lib]
name = "pixiv_tag_downloader"
path = "src/lib.rs"

[[bin]]
name = "PixivTagDownloader"
path = "src/main.rs"

[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# HTTP 客户端
reqwest = { version = "0.11", features = ["json", "stream", "cookies"] }

# 序列化/反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# 命令行参数解析
clap = { version = "4.0", features = ["derive", "color"] }

# 交互式 CLI
dialoguer = { version = "0.11", features = ["fuzzy-select"] }

# 进度条
indicatif = { version = "0.17", features = ["tokio"] }

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 日志
log = "0.4"
env_logger = "0.10"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 文件系统和路径操作
walkdir = "2.0"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# URL 处理
url = "2.0"

# 正则表达式
regex = "1.0"

# 随机数生成
rand = "0.8"

# 文件类型检测
mime_guess = "2.0"

# HTML 解析（如果需要）
scraper = "0.18"

# 字符串处理
unicode-normalization = "0.1"

# 并发控制
futures = "0.3"
futures-util = "0.3"

# 字节处理
bytes = "1.0"

# 配置文件监控
notify = "6.0"

[dev-dependencies]
tempfile = "3.0"
mockito = "1.0"
criterion = { version = "0.5", features = ["html_reports"] }



[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true

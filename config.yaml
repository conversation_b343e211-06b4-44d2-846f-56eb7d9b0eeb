# Pixiv 标签下载器配置文件
# 请根据需要修改以下配置项

# 认证配置
auth:
  # 推荐方式：完整的 Cookie 字符串
  # 获取方法：
  # 1. 登录 Pixiv 网站
  # 2. 打开浏览器开发者工具 (F12)
  # 3. 转到 Network 标签页，刷新页面
  # 4. 找到任意一个请求，在 Request Headers 中复制完整的 Cookie 值
  # 示例格式："PHPSESSID=xxx;__cf_bm=xxx;_ga=xxx;device_token=xxx;..."
  cookie: "__cf_bm=sMQah7swDml.CyPupyQlSr5O42gtgg_SYuhT5EHW2Iw-1752431165-1.0.1.1-EM.RapTVUGbYTAzkdMYCoTZenhJAhhrEjHWVrFVTl9Kz0bp2iGNL_20urGAjASYixBJ50.Mb5xqcj.nRmhguNArlHXmiiZC9bEsBTUFs3ks4ewO10E_QTTDFVZARy2as;__utma=235335808.878071517.1752431096.1752431097.1752431097.1;__utmb=235335808.3.9.1752431101292;__utmc=235335808;__utmt=1;__utmv=235335808.|2=login%20ever=no=1^3=plan=normal=1^9=p_ab_id=3=1^10=p_ab_id_2=5=1;__utmz=235335808.1752431097.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none);_cfuvid=pEkAzSdK_3PJbFZjzL.zqWhnt5aJNwkVSE3f7KRcJUU-1752431165413-0.0.1.1-604800000;_ga=GA1.1.878071517.1752431096;_ga_75BBYNYN9J=GS2.1.s1752431096$o1$g1$t1752431544$j60$l0$h0;_ga_MZ1NL4PHH0=GS2.1.s1752431166$o1$g1$t1752431543$j46$l0$h0;_gcl_au=1.1.854501375.1752431096;a_type=0;b_type=1;c_type=49;cc1=2025-07-14%2003%3A26%3A05;device_token=76154413f708581c4843c9e8350a0dff;p_ab_d_id=1078543913;p_ab_id=3;p_ab_id_2=5;PHPSESSID=18104148_N7kXqTvjR6nkmkSDTaka5m0ANaLpnCQk;privacy_policy_agreement=7;privacy_policy_notification=0;first_visit_datetime_pc=2025-07-14%2003%3A26%3A05;yuid_b=NnMCMwA"

  # 传统方式（向后兼容，不推荐）
  # 如果不使用完整 Cookie，可以单独设置这两个字段
  # session_id: "your_session_id_here"
  # csrf_token: "your_csrf_token_here"

# 下载配置
download:
  # 并发下载数量（建议 1-5，过高可能被限制）
  concurrency: 3
  # 请求延迟范围（秒），用于避免触发反爬虫机制
  delay_range: [1, 3]
  # 重试次数
  retry_count: 3
  # 超时时间（秒）
  timeout: 30

# 文件组织配置
storage:
  # 输出根目录
  output_dir: "./Output"

  # 插画作品配置
  illust:
    # 单图插画配置
    single_page:
      # 路径模板，支持变量：{uid}, {username}, {series_title_or_未分类} 等
      path_template: "{uid}_{username}/Illust/{series_title_or_未分类}"
      # 文件名模板，支持变量：{upload_date}, {pid}, {title}, {ext} 等
      filename_template: "{upload_date:%Y%m%d}_{pid}_{title}.{ext}"
    # 多图插画配置
    multi_page:
      path_template: "{uid}_{username}/Illust/{series_title_or_未分类}/{pid}_{title}"
      filename_template: "{upload_date:%Y%m%d}_{pid}_p{page_index}_{title}.{ext}"

  # 漫画作品配置
  manga:
    # 单图漫画配置
    single_page:
      path_template: "{uid}_{username}/Manga/{series_title_or_未分类}"
      filename_template: "{upload_date:%Y%m%d}_{pid}_{title}.{ext}"
    # 多图漫画配置
    multi_page:
      path_template: "{uid}_{username}/Manga/{series_title_or_未分类}/{pid}_{title}"
      filename_template: "{upload_date:%Y%m%d}_{pid}_p{page_index}_{title}.{ext}"

  # 小说配置
  novel:
    path_template: "{uid}_{username}/Novel/{series_title_or_未分类}"
    filename_template: "{upload_date:%Y%m%d}_{pid}_{title}.txt"

  # 文件冲突处理策略
  # skip: 跳过已存在文件
  # overwrite: 覆盖已存在文件
  # rename: 重命名新文件
  conflict_resolution: "skip"

# 网络配置
network:
  # User-Agent 字符串
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
  # Referer 头
  referer: "https://www.pixiv.net/"
  # HTTP 代理（可选）
  # proxy: "http://127.0.0.1:8080"

# 日志配置
logging:
  # 日志级别：trace, debug, info, warn, error
  level: "info"
  # 日志文件路径（可选）
  file: "./pixiv_downloader.log"
  # 是否输出到控制台
  console: true

# 支持的路径模板变量说明：
# {uid} - Pixiv 用户 ID
# {username} - Pixiv 用户名（文件名安全处理）
# {pid} - 作品 ID
# {title} - 作品标题（文件名安全处理）
# {type} - 作品类型（Illust, Manga, Novel）
# {page_index} - 图片页码索引号（p0, p1, p2...）
# {page_count} - 作品总页数
# {series_title} - 系列名称（如果存在）
# {series_id} - 系列 ID（如果存在）
# {upload_date} - 上传日期，支持格式化（如 {upload_date:%Y-%m-%d}）
# {tags} - 作品标签（可指定分隔符，如 {tags:_}）
# {r18} - 是否为 R18 作品
# {like_count} - 点赞数
# {bookmark_count} - 收藏数
# {ext} - 文件扩展名
